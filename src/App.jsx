import { useState, useEffect } from 'react'
import Timeline from './components/Timeline'
import timelineConfig from './data/timeline-config.json'
import './App.css'

function App() {
  const [config, setConfig] = useState(null)

  useEffect(() => {
    // Load configuration (in a real app, this could be from an API)
    setConfig(timelineConfig)
  }, [])

  if (!config) {
    return <div>Loading...</div>
  }

  return <Timeline config={config} />
}

export default App
