import { useState } from 'react'
import Timeline from '../components/Timeline'
import defaultConfig from '../data/timeline-config.json'
import darkConfig from '../data/dark-theme-config.json'
import colorfulConfig from '../data/colorful-theme-config.json'

const ThemeSwitcher = () => {
  const [currentConfig, setCurrentConfig] = useState(defaultConfig)
  const [selectedTheme, setSelectedTheme] = useState('default')

  const themes = {
    default: { name: 'Default Green', config: defaultConfig },
    dark: { name: 'Dark Theme', config: darkConfig },
    colorful: { name: 'Colorful Pink', config: colorfulConfig }
  }

  const handleThemeChange = (themeKey) => {
    setSelectedTheme(themeKey)
    setCurrentConfig(themes[themeKey].config)
  }

  return (
    <div>
      <div style={{ 
        position: 'fixed', 
        top: '20px', 
        right: '20px', 
        zIndex: 1000,
        background: 'white',
        padding: '10px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <label htmlFor="theme-select" style={{ marginRight: '10px', fontWeight: 'bold' }}>
          Theme:
        </label>
        <select 
          id="theme-select"
          value={selectedTheme} 
          onChange={(e) => handleThemeChange(e.target.value)}
          style={{ padding: '5px', borderRadius: '4px' }}
        >
          {Object.entries(themes).map(([key, theme]) => (
            <option key={key} value={key}>
              {theme.name}
            </option>
          ))}
        </select>
      </div>
      
      <Timeline config={currentConfig} />
    </div>
  )
}

export default ThemeSwitcher
