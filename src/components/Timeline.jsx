import { useEffect, useRef } from 'react';
import TimelineItem from './TimelineItem';

const Timeline = () => {
  const timelineRef = useRef(null);

  const timelineData = [
    {
      year: '2016',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias debitis praesentium itaque veniam dolorum cupiditate asperiores optio beatae, qui perferendis ipsam odit modi nemo natus corrupti neque quos distinctio facilis?'
    },
    {
      year: '2015',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati quas, reiciendis quis sequi voluptatem consectetur adipisci accusamus hic vel vero ea ad iure! Natus, ipsum, enim aspernatur fugit voluptatibus similique?'
    },
    {
      year: '2012',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Officiis, expedita. Dolorem blanditiis, delectus omnis eos accusamus mollitia et cupiditate officia maxime vel, nesciunt alias eius, quibusdam in ea eveniet ut!'
    },
    {
      year: '2010',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Veritatis voluptas voluptatum dolorum, quibusdam dignissimos animi pariatur laboriosam quis explicabo similique aperiam debitis quam velit quod, reprehenderit harum ratione. Iste, unde?'
    },
    {
      year: '2008',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Amet inventore odit placeat in laboriosam dolore ducimus vero, sapiente ipsam veritatis, numquam libero itaque dolores natus ex aliquam nam nihil cumque.'
    },
    {
      year: '2007',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Odit sequi nobis, blanditiis quae dolorem quasi reiciendis odio qui fugit? Officiis quos aspernatur mollitia dolorum pariatur repellendus quaerat dolorem magnam quo.'
    },
    {
      year: '2004',
      content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vel corporis sunt nostrum velit quibusdam neque porro ratione quos dolor libero. Tempore consequatur natus nostrum delectus provident fugiat corporis error ipsa.'
    }
  ];

  useEffect(() => {
    // Initial check for elements that should be hidden
    const checkInitialVisibility = () => {
      const contentElements = timelineRef.current?.querySelectorAll('.content');

      if (contentElements) {
        contentElements.forEach((element) => {
          const rect = element.getBoundingClientRect();
          const bottomOfObject = rect.top + rect.height;
          const bottomOfWindow = window.innerHeight;

          if (bottomOfObject > bottomOfWindow) {
            element.classList.add('hidden');
          }
        });
      }
    };

    // Scroll event handler
    const handleScroll = () => {
      const hiddenElements = timelineRef.current?.querySelectorAll('.hidden');

      if (hiddenElements) {
        hiddenElements.forEach((element) => {
          const rect = element.getBoundingClientRect();
          const bottomOfObject = rect.top + rect.height;
          const bottomOfWindow = window.innerHeight;

          if (bottomOfWindow > bottomOfObject) {
            element.style.transition = 'opacity 0.7s ease';
            element.style.opacity = '1';
            element.classList.remove('hidden');
          }
        });
      }
    };

    // Run initial check
    checkInitialVisibility();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section className="timeline" ref={timelineRef}>
      <h1>Responsive Timeline</h1>
      <ul>
        {timelineData.map((item, index) => (
          <TimelineItem 
            key={index}
            year={item.year}
            content={item.content}
          />
        ))}
      </ul>
    </section>
  );
};

export default Timeline;
